#!/usr/bin/env python3
"""
模型配置和结构检查脚本
用于检查当前项目使用的模型配置、已下载的模型文件，以及 FastEmbed 支持情况
"""

import os
import json
from pathlib import Path
import sys

def check_rag_config():
    """检查 RAG 服务配置"""
    print('📋 RAG 服务配置:')
    try:
        # 读取 RAG 服务文件
        rag_file = Path('backend/app/services/rag_service.py')
        if rag_file.exists():
            with open(rag_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找 fastembed_sparse_model 配置
            lines = content.split('\n')
            for i, line in enumerate(lines):
                if 'fastembed_sparse_model' in line:
                    print(f'   第 {i+1} 行: {line.strip()}')
                    break
        else:
            print('   ❌ RAG 服务文件不存在')
    except Exception as e:
        print(f'   ❌ 读取配置失败: {e}')

def check_huggingface_cache():
    """检查 HuggingFace 缓存目录"""
    print(f'\n📁 HuggingFace 缓存目录:')
    
    # 可能的缓存目录
    cache_dirs = [
        Path('/root/.cache/huggingface/hub'),
        Path.home() / '.cache/huggingface/hub',
        Path(os.environ.get('HF_HOME', '')) / 'hub' if os.environ.get('HF_HOME') else None
    ]
    
    cache_dir = None
    for dir_path in cache_dirs:
        if dir_path and dir_path.exists():
            cache_dir = dir_path
            break
    
    if cache_dir:
        print(f'   路径: {cache_dir}')
        print(f'   存在: {cache_dir.exists()}')
        
        models = [d for d in cache_dir.iterdir() if d.is_dir()]
        print(f'   发现 {len(models)} 个模型:')
        for model in models:
            print(f'     - {model.name}')
        
        return cache_dir
    else:
        print('   ❌ 未找到 HuggingFace 缓存目录')
        return None

def check_model_structure(cache_dir, model_name):
    """检查特定模型的结构"""
    print(f'\n📦 {model_name} 模型结构:')
    
    model_path = cache_dir / f'models--{model_name.replace("/", "--")}'
    if model_path.exists():
        print(f'   路径: {model_path}')
        snapshots = list(model_path.iterdir())
        if snapshots:
            snapshot = snapshots[0]
            print(f'   快照: {snapshot.name}')
            files = list(snapshot.iterdir())
            print(f'   文件: {[f.name for f in files]}')
            
            # 检查关键文件
            config_file = snapshot / 'config.json'
            if config_file.exists():
                try:
                    with open(config_file, 'r') as f:
                        config = json.load(f)
                    print(f'   模型类型: {config.get("model_type", "未知")}')
                    print(f'   架构: {config.get("architectures", ["未知"])[0] if config.get("architectures") else "未知"}')
                    
                    # 检查模型大小
                    model_files = [f for f in files if f.name.endswith(('.bin', '.safetensors', '.onnx'))]
                    total_size = sum(f.stat().st_size for f in model_files)
                    print(f'   模型文件大小: {total_size / (1024*1024):.2f} MB')
                    
                except Exception as e:
                    print(f'   配置读取失败: {e}')
        else:
            print('   ❌ 没有快照目录')
    else:
        print(f'   ❌ 模型不存在: {model_path}')

def check_fastembed_support():
    """检查 FastEmbed 支持的模型"""
    print(f'\n🚀 FastEmbed 支持检查:')
    try:
        from fastembed import TextEmbedding
        models = TextEmbedding.list_supported_models()
        print(f'   支持的模型数量: {len(models)}')
        print('   支持的模型列表:')
        for i, model in enumerate(models):
            print(f'     {i+1:2d}. {model}')
            
        # 检查当前配置的模型是否支持
        current_model = "Qdrant/bm25"
        if current_model in models:
            print(f'   ✅ 当前配置的模型 "{current_model}" 在支持列表中')
        else:
            print(f'   ❌ 当前配置的模型 "{current_model}" 不在支持列表中')
            print('   建议使用以下模型之一:')
            for model in models[:5]:  # 显示前5个建议
                print(f'     - {model}')
                
    except Exception as e:
        print(f'   ❌ FastEmbed 检查失败: {e}')

def check_environment():
    """检查环境变量"""
    print(f'\n🔧 环境变量:')
    env_vars = [
        'HF_HUB_OFFLINE',
        'HF_HOME',
        'TRANSFORMERS_CACHE',
        'HF_DATASETS_CACHE'
    ]
    
    for var in env_vars:
        value = os.environ.get(var, "未设置")
        print(f'   {var}: {value}')

def check_qdrant_collection():
    """检查 Qdrant 集合状态"""
    print(f'\n🗄️ Qdrant 集合状态:')
    try:
        import qdrant_client
        from backend.config.settings import settings
        
        client = qdrant_client.QdrantClient(
            host=settings.qdrant_host,
            port=settings.qdrant_port,
            api_key=settings.qdrant_api_key if settings.qdrant_api_key else None
        )
        
        collections = client.get_collections()
        print(f'   集合数量: {len(collections.collections)}')
        
        for collection in collections.collections:
            print(f'   - {collection.name}')
            try:
                info = client.get_collection(collection.name)
                print(f'     点数: {info.points_count}')
                print(f'     向量配置: {info.config.params.vectors}')
            except Exception as e:
                print(f'     获取信息失败: {e}')
                
    except Exception as e:
        print(f'   ❌ Qdrant 检查失败: {e}')

def main():
    """主函数"""
    print('🔍 当前模型配置和结构检查')
    print('=' * 60)
    
    # 检查当前工作目录
    print(f'📂 当前工作目录: {Path.cwd()}')
    
    # 1. 检查 RAG 配置
    check_rag_config()
    
    # 2. 检查 HuggingFace 缓存
    cache_dir = check_huggingface_cache()
    
    # 3. 检查特定模型结构
    if cache_dir:
        check_model_structure(cache_dir, "Qdrant/bm25")
        check_model_structure(cache_dir, "BAAI/bge-small-en-v1.5")
        check_model_structure(cache_dir, "sentence-transformers/all-MiniLM-L6-v2")
    
    # 4. 检查 FastEmbed 支持
    check_fastembed_support()
    
    # 5. 检查环境变量
    check_environment()
    
    # 6. 检查 Qdrant 集合
    check_qdrant_collection()
    
    print(f'\n✅ 检查完成!')
    print('=' * 60)

if __name__ == "__main__":
    main() 