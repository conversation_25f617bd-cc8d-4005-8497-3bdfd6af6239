#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HuggingFace模型下载工具
"""

import os
import requests
from pathlib import Path
import json
from tqdm import tqdm
import time

def download_file(url, local_path, max_retries=3):
    """下载单个文件，支持断点续传"""
    
    local_path = Path(local_path)
    local_path.parent.mkdir(parents=True, exist_ok=True)
    
    headers = {}
    resume_pos = 0
    
    # 检查是否存在部分下载的文件
    if local_path.exists():
        resume_pos = local_path.stat().st_size
        headers['Range'] = f'bytes={resume_pos}-'
    
    for attempt in range(max_retries):
        try:
            print(f"下载: {url}")
            if resume_pos > 0:
                print(f"从位置 {resume_pos} 继续下载...")
            
            response = requests.get(url, headers=headers, stream=True, timeout=30)
            
            if response.status_code in [200, 206]:  # 200=完整下载, 206=部分下载
                total_size = int(response.headers.get('content-length', 0))
                if resume_pos > 0:
                    total_size += resume_pos
                
                mode = 'ab' if resume_pos > 0 else 'wb'
                
                with open(local_path, mode) as f:
                    with tqdm(total=total_size, initial=resume_pos, unit='B', unit_scale=True) as pbar:
                        for chunk in response.iter_content(chunk_size=8192):
                            if chunk:
                                f.write(chunk)
                                pbar.update(len(chunk))
                
                print(f"✅ 下载完成: {local_path.name}")
                return True
                
            elif response.status_code == 404:
                print(f"⚠️ 文件不存在: {local_path.name}")
                return False
            else:
                print(f"❌ HTTP错误 {response.status_code}: {local_path.name}")
                
        except Exception as e:
            print(f"❌ 下载失败 (尝试 {attempt + 1}/{max_retries}): {e}")
            if attempt < max_retries - 1:
                time.sleep(2)
    
    return False

def download_model(repo_id, local_dir):
    """下载完整的HuggingFace模型"""
    
    print(f"\n=== 下载模型: {repo_id} ===")
    
    # 创建模型目录
    model_dir = Path(local_dir) / repo_id.replace('/', '--')
    model_dir.mkdir(parents=True, exist_ok=True)
    
    # 需要下载的文件列表
    files_to_download = [
        'config.json',
        'tokenizer.json', 
        'tokenizer_config.json',
        'vocab.txt',
        'special_tokens_map.json',
        'pytorch_model.bin',
        'model.safetensors',
        'tf_model.h5',
        'model.onnx',
        'README.md'
    ]
    
    success_count = 0
    
    for filename in files_to_download:
        file_url = f"https://huggingface.co/{repo_id}/resolve/main/{filename}"
        file_path = model_dir / filename
        
        if download_file(file_url, file_path):
            success_count += 1
    
    print(f"\n✅ 模型 {repo_id} 下载完成!")
    print(f"   成功下载 {success_count} 个文件")
    print(f"   保存位置: {model_dir}")
    
    return model_dir

def main():
    """主函数"""
    
    # 推荐的轻量级嵌入模型
    models = [
        "sentence-transformers/all-MiniLM-L6-v2",  # 22MB, 很好的性能
        "BAAI/bge-small-en-v1.5",                  # 133MB, 更好的性能
        "sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2"  # 支持中文
    ]
    
    # 创建下载目录
    download_dir = Path("./huggingface_models")
    download_dir.mkdir(exist_ok=True)
    
    print("🚀 开始下载HuggingFace模型...")
    print(f"📁 下载目录: {download_dir.absolute()}")
    
    successful_downloads = []
    
    for model_id in models:
        try:
            result_dir = download_model(model_id, download_dir)
            if result_dir:
                successful_downloads.append(model_id)
        except KeyboardInterrupt:
            print("\n⏹️ 用户中断下载")
            break
        except Exception as e:
            print(f"❌ 下载 {model_id} 时出错: {e}")
    
    # 总结
    print(f"\n🎉 下载完成!")
    print(f"✅ 成功下载 {len(successful_downloads)} 个模型:")
    for model in successful_downloads:
        print(f"   - {model}")
    
    if successful_downloads:
        print(f"\n📦 打包命令:")
        print(f"tar -czf huggingface_models.tar.gz huggingface_models/")
        print(f"\n📤 上传命令:")
        print(f"scp huggingface_models.tar.gz root@your_server_ip:/tmp/")

if __name__ == "__main__":
    main()
