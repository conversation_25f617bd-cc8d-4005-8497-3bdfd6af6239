"""
应用配置设置
"""
from typing import List
from pathlib import Path
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """应用配置类"""

    # 应用基础配置
    app_name: str = "RAG Chat Application"
    app_version: str = "1.0.0"
    app_host: str = "0.0.0.0"
    app_port: int = 8000
    debug: bool = False
    environment: str = "production"

    # API配置
    api_v1_prefix: str = "/api/v1"

    # OpenAI配置
    openai_api_key: str
    openai_base_url: str = "https://api.openai.com/v1"
    openai_model: str = "gpt-4o-mini"
    embedding_model: str = "text-embedding-3-small"

    # 存储配置
    data_dir: Path = Path("./data")
    collection_name: str = "documents"

    # Qdrant配置
    qdrant_host: str = "localhost"
    qdrant_port: int = 6333
    qdrant_grpc_port: int = 6334
    qdrant_api_key: str
    qdrant_prefer_grpc: bool = True

    # Qdrant混合检索配置
    default_bm25_weight: float = 0.5  # 稀疏向量权重
    default_vector_weight: float = 0.5  # 密集向量权重
    default_similarity_top_k: int = 5  # 最终返回结果数量
    default_retrieval_mode: str = "hybrid"  # hybrid, vector_only, bm25_only

    # Qdrant稀疏向量配置
    qdrant_sparse_top_k: int = 10  # 稀疏向量检索候选数量
    qdrant_hybrid_alpha: float = 0.5  # 混合检索权重（0.5表示50%密集+50%稀疏）

    # CORS配置
    allowed_origins: List[str] = [
        "http://localhost:3000",
        "http://127.0.0.1:3000",
        "http://localhost:8000",
        "http://127.0.0.1:8000",
        "http://localhost:5500",  # Live Server默认端口
        "http://127.0.0.1:5500",  # Live Server默认端口
        "http://localhost:5501",  # Live Server备用端口
        "http://127.0.0.1:5501",  # Live Server备用端口
        "file://",  # 允许本地文件访问
        "*"  # 允许所有域名访问（开发环境）
    ]

    # 数据库配置（为CMS模块准备）
    mysql_host: str
    mysql_port: int = 3306
    mysql_user: str = "root"
    mysql_password: str
    mysql_database: str = "chestnut_cms"
    
    # ChestnutCMS配置
    chestnut_cms_base_url: str = "https://www.gzmdrw.cn"

    class Config:
        env_file = [".env.local", ".env"]
        env_file_encoding = "utf-8"
        case_sensitive = False

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # 确保目录存在
        self.data_dir.mkdir(exist_ok=True)


# 全局配置实例
settings = Settings()
