version: "3.8"

services:
  qdrant:
    image: qdrant/qdrant:latest
    container_name: qdrant
    restart: unless-stopped
    ports:
      - "6333:6333"  # HTTP API
      - "6334:6334"  # gRPC API
    volumes:
      - ./qdrant_storage:/qdrant/storage
    environment:
      - QDRANT__SERVICE__HTTP_PORT=6333
      - QDRANT__SERVICE__GRPC_PORT=6334
      # 可选：启用API密钥认证
      - QDRANT__SERVICE__API_KEY=Kwe7IgYB-Sy7D4HCtXUyqDRbgjR_y61gs6Y9aQVnRqa-zkYlwwVaOK9lZcHN4Nj6
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:6333/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - qdrant-network

networks:
  qdrant-network:
    driver: bridge

volumes:
  qdrant_storage:
    driver: local
